import 'package:get/get.dart';
import '../../../core/storage/secure_storage_service.dart';
import '../../../shared/models/device.dart';
import '../../modules/onboarding/data/repositories/binding_repository.dart';

class DeviceAuthService extends GetxService {
  final SecureStorageService secureStorage;
  final BindingRepository bindingRepository;

  DeviceAuthService({
    required this.secureStorage,
    required this.bindingRepository,
  });

  final Rxn<Device> _device = Rxn<Device>();
  String? _token;

  Device? get device => _device.value;
  String? get token => _token;

  /// Load token + device on app startup
  Future<void> init() async {
    final token = await secureStorage.readToken();
    if (token != null && token.isNotEmpty) {
      _token = token;
      try {
        final device = await bindingRepository.getDevice();
        _device.value = device;
      } catch (_) {
        await logout();
      }
    }
  }

  /// Check if authenticated
  bool get isAuthenticated => _token != null && _device.value != null;

  /// Save auth after successful bind
  Future<void> saveAuth(String token, Device device) async {
    _token = token;
    _device.value = device;
    await secureStorage.writeToken(token);
  }

  /// Get authenticated device
  Future<void> getDevice() async {
    try {
      final device = await bindingRepository.getDevice();
      _device.value = device;
    } catch (_) {
      await logout();
    }
  }

  /// Logout
  Future<void> logout() async {
    try {
      await bindingRepository.logout();
    } catch (_) {}
    _token = null;
    _device.value = null;
    await secureStorage.deleteToken();
    Get.offAllNamed('/binding');
  }

  /// Force unauthenticated state
  Future<void> handleUnauthorized() async {
    _token = null;
    _device.value = null;
    await secureStorage.deleteToken();
    Get.offAllNamed('/binding');
  }

  /// Navigate to dashboard if authenticated
  Future<void> checkAuthAndNavigate() async {
    if (isAuthenticated) {
      Get.offAllNamed('/dashboard');
    } else {
      Get.offAllNamed('/binding');
    }
  }
}
