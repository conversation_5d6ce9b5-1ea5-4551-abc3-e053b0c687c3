import 'package:get/get.dart';
import '../../../core/errors/error_handler.dart';
import '../../../core/services/device_auth_service.dart';
import '../../../shared/models/activity.dart';
import '../data/repositories/dashboard_repository.dart';
import '../utils/helpers.dart';
import '../widgets/activity_item.dart';

class DashboardController extends GetxController {
  final DeviceAuthService authService = Get.find<DeviceAuthService>();
  final DashboardRepository dashboardRepository;

  final activities = <Activity>[].obs;
  final isLoadingActivities = false.obs;

  DashboardController(this.dashboardRepository);

  @override
  void onInit() {
    super.onInit();
    _init();
  }

  Future<void> _init() async {
    await authService.getDevice();
    await getActivities();
  }

  Future<void> getActivities() async {
    isLoadingActivities.value = true;
    try {
      final result = await dashboardRepository.getRecentActivities();
      activities.assignAll(result);
    } catch (e) {
      ErrorHandler.handle(e);
    } finally {
      isLoadingActivities.value = false;
    }
  }

  List<ActivityItem> get activityItems => activities.map(mapActivityToItem).toList();
}
