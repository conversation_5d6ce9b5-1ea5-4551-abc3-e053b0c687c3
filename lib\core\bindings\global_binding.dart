import 'package:get/get.dart';
import '../../modules/onboarding/data/repositories/binding_repository.dart';
import '../services/device_auth_service.dart';
import '../storage/secure_storage_service.dart';
import '../network/dio_client.dart';

class GlobalBinding extends Bindings {
  @override
  void dependencies() {
    // Core services as singletons
    Get.put<SecureStorageService>(SecureStorageService(), permanent: true);
    Get.put<DioClient>(
      DioClient(Get.find<SecureStorageService>()), 
      permanent: true
    );
    Get.put<BindingRepository>(BindingRepository(Get.find<DioClient>()));
    Get.put<DeviceAuthService>(DeviceAuthService(
      secureStorage: Get.find<SecureStorageService>(),
      bindingRepository: Get.find<BindingRepository>(),
    ));
  }
}