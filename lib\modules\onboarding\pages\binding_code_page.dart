import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../shared/theme/app_colors.dart';
import '../../../shared/widgets/app_bar_widget.dart';
import '../../../shared/widgets/utils/uppercase_text_formatter.dart';
import '../controllers/binding_code_controller.dart';

class BindingCodePage extends StatelessWidget {
  BindingCodePage({super.key});

  final BindingCodeController controller = Get.find<BindingCodeController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBarWidget(
        title: "Device Binding",
        showBackButton: false,
        onBack: () => Get.back(),
      ),
      body: Center(
        child: SingleChildScrollView(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 800),
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 10,
                  spreadRadius: 2,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircleAvatar(
                  radius: 40,
                  backgroundColor: AppColors.primary,
                  child: Icon(
                    Icons.key_outlined,
                    size: 40,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  "Device Binding",
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  "Select your device type and enter the binding code provided by your school administrator to connect this device to the monitoring system.",
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.black54, fontSize: 14),
                ),
                const SizedBox(height: 24),

                /// Device Type Dropdown
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "Device Type",
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Obx(() {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DropdownButtonFormField<String>(
                        style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                        decoration: InputDecoration(
                          errorText: controller.deviceTypeError.value.isEmpty
                              ? null
                              : controller.deviceTypeError.value,
                        ),
                        isExpanded: true,
                        hint: const Text('Select device type'),
                        initialValue: controller.isDeviceTypesLoading.value
                            ? "Loading..."
                            : (controller.deviceType.value.isEmpty
                                ? null
                                : controller.deviceType.value),
                        items: controller.deviceTypeNames
                            .map(
                              (type) => DropdownMenuItem(
                                value: type,
                                child: Text(type),
                              ),
                            )
                            .toList(),
                        onChanged: (val) {
                          if (val != null) {
                            controller.setDeviceType(val);
                            // Clear error when user selects an option
                            if (controller.deviceTypeError.isNotEmpty) {
                              controller.deviceTypeError.value = '';
                            }
                          }
                        },
                      ),
                    ],
                  );
                }),
                const SizedBox(height: 16),

                /// Binding Code Input
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "Binding Code",
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Obx(
                  () => Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextField(
                        textAlign: TextAlign.center,
                        style: GoogleFonts.robotoMono(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                        decoration: InputDecoration(
                          hintText: "Enter 8-character binding code",
                          hintStyle: GoogleFonts.robotoMono(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          errorText: controller.bindingCodeError.value.isEmpty
                              ? null
                              : controller.bindingCodeError.value,
                        ),
                        maxLength: 8,
                        inputFormatters: [UpperCaseTextFormatter()],
                        onChanged: (value) {
                          controller.setBindingCode(value.toUpperCase());
                          // Clear error when user starts typing
                          if (controller.bindingCodeError.isNotEmpty &&
                              value.isNotEmpty) {
                            controller.bindingCodeError.value = '';
                          }
                        },
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                /// Info Box
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.lightBlue200),
                    color: AppColors.lightBlue50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: const [
                      Text(
                        "Important Information",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.darkBlue900,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        "• Select the correct device type for proper configuration",
                        style: TextStyle(color: AppColors.darkBlue800),
                      ),
                      Text(
                        "• Binding codes are unique to each device",
                        style: TextStyle(color: AppColors.darkBlue800),
                      ),
                      Text(
                        "• Codes expire after 24 hours if unused",
                        style: TextStyle(color: AppColors.darkBlue800),
                      ),
                      Text(
                        "• Contact your administrator if you encounter issues",
                        style: TextStyle(color: AppColors.darkBlue800),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                /// Submit Button
                Obx(
                  () => ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      minimumSize: const Size.fromHeight(55),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    onPressed: controller.isLoading.value
                        ? null
                        : () => controller.validateBinding(),
                    child: controller.isLoading.value
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : const Text(
                            "Bind Device",
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                            ),
                          ),
                  ),
                ),
                const SizedBox(height: 24),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Wrap(
                    alignment: WrapAlignment.center,
                    crossAxisAlignment: WrapCrossAlignment.center,
                    spacing: 4,
                    children: [
                      const Text("Don't have a binding code?"),
                      TextButton(
                        onPressed: () {
                          // TODO: Add contact administrator action
                        },
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.zero,
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: const Text(
                          "Contact Administrator",
                          style: TextStyle(
                            color: AppColors.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
