import 'package:flutter/material.dart';

import '../../../../shared/theme/app_colors.dart';
import '../../data/models/diagnostic_data.dart';

class DiagnosticRun extends StatelessWidget {
  final DiagnosticRunData? data;

  const DiagnosticRun({super.key, this.data});

  Widget _buildInfoItem({
    required String label,
    required String value,
    required bool isStatus,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(label, style: const TextStyle(fontSize: 14, color: Colors.grey)),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isStatus ? FontWeight.w500 : FontWeight.bold,
            color: isStatus ? AppColors.primary : null,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final isWide = MediaQuery.of(context).size.width > 800;
    final diagnosticRunData = data ?? DiagnosticRunData.mock();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.grey.shade100.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "Diagnostic Run",
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: isWide ? 4 : 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              mainAxisExtent: 45,
              childAspectRatio: 2.5,
            ),
            itemCount: 4,
            itemBuilder: (context, index) {
              switch (index) {
                case 0:
                  return _buildInfoItem(
                    label: 'Date',
                    value: diagnosticRunData.formattedDate,
                    isStatus: false,
                  );
                case 1:
                  return _buildInfoItem(
                    label: 'Duration',
                    value: diagnosticRunData.formattedDuration,
                    isStatus: false,
                  );
                case 2:
                  return _buildInfoItem(
                    label: 'Tests Run',
                    value: '${diagnosticRunData.testsRun} Tests',
                    isStatus: false,
                  );
                case 3:
                default:
                  return _buildInfoItem(
                    label: 'Status',
                    value: diagnosticRunData.status,
                    isStatus: true,
                  );
              }
            },
          ),
        ],
      ),
    );
  }
}
