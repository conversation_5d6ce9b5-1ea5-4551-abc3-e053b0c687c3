// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Device _$DeviceFromJson(Map<String, dynamic> json) => Device(
  id: (json['id'] as num).toInt(),
  code: json['code'] as String,
  location: json['location'] as String,
  lastReportedAt: DateTime.parse(json['lastReportedAt'] as String),
  deviceStatus: json['deviceStatus'] as String,
  status: json['status'] as String,
  deviceTypeId: (json['deviceTypeId'] as num).toInt(),
  deviceType: json['deviceType'] as String,
  reports:
      (json['reports'] as List<dynamic>?)
          ?.map(
            (e) => e == null
                ? null
                : DeviceReport.fromJson(e as Map<String, dynamic>),
          )
          .toList() ??
      const [],
  school: json['school'] == null
      ? null
      : School.fromJson(json['school'] as Map<String, dynamic>),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$DeviceToJson(Device instance) => <String, dynamic>{
  'id': instance.id,
  'code': instance.code,
  'location': instance.location,
  'lastReportedAt': instance.lastReportedAt.toIso8601String(),
  'deviceStatus': instance.deviceStatus,
  'status': instance.status,
  'deviceTypeId': instance.deviceTypeId,
  'deviceType': instance.deviceType,
  'reports': instance.reports,
  'school': instance.school,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};
