import 'package:json_annotation/json_annotation.dart';
import 'device.dart';

part 'activity.g.dart';

@JsonSerializable()
class Activity {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'userId')
  final int? userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'deviceId')
  final int? deviceId;
  final String subject;
  final String? description;
  final String type;
  final String? status;
  final String? icon;
  @Json<PERSON><PERSON>(includeIfNull: false)
  final dynamic user;
  @JsonKey(includeIfNull: false)
  final Device? device;
  @Json<PERSON>ey(name: 'createdAt')
  final DateTime createdAt;
  @J<PERSON><PERSON>ey(name: 'updatedAt')
  final DateTime updatedAt;

  const Activity({
    required this.id,
    this.userId,
    required this.deviceId,
    required this.subject,
    this.description,
    required this.type,
    this.status,
    this.icon,
    this.user,
    this.device,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Connect the generated [_$ActivityFromJson] function to the `fromJson`
  factory Activity.fromJson(Map<String, dynamic> json) => 
      _$ActivityFromJson(json);

  /// Connect the generated [_$ActivityToJson] function to the `toJson` method
  Map<String, dynamic> toJson() => _$ActivityToJson(this);
}