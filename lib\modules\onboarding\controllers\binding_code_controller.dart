import 'package:get/get.dart';
import 'package:device_info_plus/device_info_plus.dart';

import '../../../core/errors/app_exception.dart';
import '../../../core/errors/error_handler.dart';
import '../../../core/services/device_auth_service.dart';
import '../../../core/storage/secure_storage_service.dart';
import '../../../shared/models/binding_code.dart';
import '../../../shared/models/device.dart';
import '../data/repositories/binding_repository.dart';

class BindingCodeController extends GetxController {
  final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
  final BindingRepository _bindingRepository;
  final SecureStorageService secureStorage;
  final DeviceAuthService authService = Get.find<DeviceAuthService>();

  final Rxn<Device> _device = Rxn<Device>();
  String? _token;

  Device? get device => _device.value;
  String? get token => _token;

  BindingCodeController(this._bindingRepository, this.secureStorage);

  var deviceType = ''.obs;
  var bindingCode = ''.obs;
  var deviceTypeError = ''.obs;
  var bindingCodeError = ''.obs;
  var isLoading = false.obs;

  var deviceTypes = <Map<String, dynamic>>[].obs;
  var deviceTypeNames = <String>[].obs;
  var isDeviceTypesLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    fetchDeviceTypes();
    // _loadTokenFromStorage();
    // getDeviceInfo();
  }

  // Future<void> getDeviceInfo() async {
  //   try {
  //     BaseDeviceInfo deviceInfo = await deviceInfoPlugin.deviceInfo;
  //     print('Running on ${deviceInfo.data}');
  //   } catch (e) {
  //     print('Error getting device info: $e');
  //   }
  // }

  Future<void> _loadTokenFromStorage() async {
    final token = await secureStorage.readToken();
    if (token != null && token.isNotEmpty) {
      _token = token;
      try {
        final device = await _bindingRepository.getDevice();
        _device.value = device;

        Get.offAllNamed('/dashboard');
      } catch (_) {
        await logout();
      }
    }
  }

  Future<void> fetchDeviceTypes() async {
    isDeviceTypesLoading.value = true;
    try {
      final types = await _bindingRepository.getDeviceTypes();
      deviceTypes.assignAll(types);
      deviceTypeNames.assignAll(types.map((e) => e["name"].toString()));
    } catch (e) {
      ErrorHandler.handle(e);
    } finally {
      isDeviceTypesLoading.value = false;
    }
  }

  void setDeviceType(String value) {
    deviceType.value = value;
  }

  void setBindingCode(String value) {
    bindingCode.value = value.toUpperCase();
  }

  Future<void> validateBinding() async {
    // Clear previous errors
    deviceTypeError.value = '';
    bindingCodeError.value = '';
    
    bool isValid = true;
    
    if (deviceType.isEmpty) {
      deviceTypeError.value = 'Please select a device type';
      isValid = false;
    }
    
    if (bindingCode.value.length != 8) {
      bindingCodeError.value = 'Binding code must be exactly 8 characters';
      isValid = false;
    }
    
    if (!isValid) return;

    isLoading.value = true;
    
    try {
      final BindingCode binding = await _bindingRepository.validateBindingCode(
        bindingCode: bindingCode.value,
        deviceType: deviceType.value,
      );

      
      Get.toNamed(
        '/binding/confirmation',
        arguments: {
          'schoolName': binding.school?.name,
          'schoolLocation': "${binding.school?.address}, ${binding.school?.localGovernmentArea?.name} LGA",
          'bindingCode': binding.code,
          'deviceType': binding.deviceType,
          'status': 'Active',
        },
      );
    } catch (e) {
      ErrorHandler.handle(e);
      // ErrorHandler.handle(e, retry: submit);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> bindDevice() async {
    isLoading.value = true;
    try {
      final data = await _bindingRepository.bindDevice(
        bindingCode: bindingCode.value,
        deviceType: deviceType.value,
      );

      final token = data['token'] as String? ?? data['access_token'] as String?;
      final deviceJson = data['device'] as Map<String, dynamic>? ?? data['data'] as Map<String, dynamic>?;

      if (token == null) throw ServerException('Token missing in response');

      final device = Device.fromJson(deviceJson!);
      await authService.saveAuth(token, device);
 
      Get.offAllNamed('/binding/success');
    } catch (e) {
      ErrorHandler.handle(e);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> logout() async {
    await _bindingRepository.logout();
    _token = null;
    _device.value = null;
    await secureStorage.deleteToken();
    Get.offAllNamed('/binding');
  }

  Future<void> handleUnauthorized() async {
    _token = null;
    _device.value = null;
    secureStorage.deleteToken();
    Get.offAllNamed('/binding');
  }
}
