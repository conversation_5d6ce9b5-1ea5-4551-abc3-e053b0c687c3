import 'package:get/get.dart';
import '../../../core/network/dio_client.dart';
import '../../../core/storage/secure_storage_service.dart';
import '../controllers/binding_code_controller.dart';
import '../controllers/onboarding_controller.dart';
import '../controllers/version_controller.dart';
import '../data/repositories/app_version_repository.dart';
import '../data/repositories/binding_repository.dart';

class OnboardingBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<AppVersionRepository>(() => AppVersionRepository(Get.find<DioClient>()));
    Get.lazyPut<OnboardingController>(() => OnboardingController(dioClient: Get.find<DioClient>()));
    Get.lazyPut<BindingRepository>(() => BindingRepository(Get.find<DioClient>()));
    Get.lazyPut<BindingCodeController>(() => BindingCodeController(Get.find<BindingRepository>(), Get.find<SecureStorageService>()));
    Get.lazyPut<VersionController>(() => VersionController(appVersionRepository: Get.find<AppVersionRepository>()));
  }
}