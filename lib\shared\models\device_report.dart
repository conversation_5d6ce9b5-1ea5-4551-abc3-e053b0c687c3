import 'package:json_annotation/json_annotation.dart';

import 'device.dart';

part 'device_report.g.dart';

@JsonSerializable()
class DeviceReport {
  final int id;
  @Json<PERSON><PERSON>(name: 'deviceId')
  final int deviceId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'signalStrength')
  final int signalStrength;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'batteryLevel')
  final int batteryLevel;
  final int temperature;
  @Json<PERSON>ey(name: 'signalStatus')
  final String signalStatus;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'diskSize')
  final int diskSize;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'diskFree')
  final int diskFree;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'ramSize')
  final int ramSize;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'ramFree')
  final int ramFree;
  final Device? device;
  @Json<PERSON>ey(name: 'createdAt')
  final DateTime createdAt;
  @Json<PERSON>ey(name: 'updatedAt')
  final DateTime updatedAt;

  const DeviceReport({
    required this.id,
    required this.deviceId,
    required this.signalStrength,
    required this.batteryLevel,
    required this.temperature,
    required this.signalStatus,
    required this.diskSize,
    required this.diskFree,
    required this.ramSize,
    required this.ramFree,
    this.device,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Connect the generated [_$DeviceReportFromJson] function to the `fromJson`
  /// factory.
  factory DeviceReport.fromJson(Map<String, dynamic> json) => 
      _$DeviceReportFromJson(json);

  /// Connect the generated [_$DeviceReportToJson] function to the `toJson` method.
  Map<String, dynamic> toJson() => _$DeviceReportToJson(this);

  @override
  String toString() {
    return 'DeviceReport{id: $id, deviceId: $deviceId, battery: $batteryLevel%, signal: $signalStrength%}';
  }

  /// Creates a copy of the DeviceReport with the given fields replaced by the non-null values.
  DeviceReport copyWith({
    int? id,
    int? deviceId,
    int? signalStrength,
    int? batteryLevel,
    int? temperature,
    String? signalStatus,
    int? diskSize,
    int? diskFree,
    int? ramSize,
    int? ramFree,
    Device? device,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DeviceReport(
      id: id ?? this.id,
      deviceId: deviceId ?? this.deviceId,
      signalStrength: signalStrength ?? this.signalStrength,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      temperature: temperature ?? this.temperature,
      signalStatus: signalStatus ?? this.signalStatus,
      diskSize: diskSize ?? this.diskSize,
      diskFree: diskFree ?? this.diskFree,
      ramSize: ramSize ?? this.ramSize,
      ramFree: ramFree ?? this.ramFree,
      device: device ?? this.device,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Returns disk usage percentage
  double get diskUsagePercentage {
    if (diskSize == 0) return 0.0;
    return ((diskSize - diskFree) / diskSize) * 100;
  }

  /// Returns RAM usage percentage
  double get ramUsagePercentage {
    if (ramSize == 0) return 0.0;
    return ((ramSize - ramFree) / ramSize) * 100;
  }

  /// Returns true if battery level is critical (below 15%)
  bool get isBatteryCritical => batteryLevel < 15;

  /// Returns true if temperature is critical (above 80°C)
  bool get isTemperatureCritical => temperature > 80;
}