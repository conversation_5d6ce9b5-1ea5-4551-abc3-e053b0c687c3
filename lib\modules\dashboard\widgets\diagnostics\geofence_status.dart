import 'package:flutter/material.dart';

import '../../../../shared/theme/app_colors.dart';
import '../../data/models/diagnostic_data.dart';

class GeofenceStatus extends StatelessWidget {
  final bool fixedHeight;
  final GeofenceData? data;

  const GeofenceStatus({super.key, this.fixedHeight = false, this.data});

  @override
  Widget build(BuildContext context) {
    final geofenceData = data ?? GeofenceData.mock();
    final isWithinGeofence = geofenceData.isWithinGeofence;
    final isMonitoringActive = geofenceData.isMonitoringActive;

    return Container(
      height: fixedHeight ? 200 : null,
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: isWithinGeofence ? AppColors.lightGreen50 : Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isWithinGeofence
              ? AppColors.lightGreen200
              : Colors.red.shade200,
        ),
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                isWithinGeofence
                    ? Icons.check_circle_outline_rounded
                    : Icons.warning_amber_rounded,
                color: isWithinGeofence
                    ? const Color(0xFF38a169)
                    : Colors.red.shade600,
                size: 30,
              ),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Geofence Status",
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    geofenceData.description,
                    style: TextStyle(fontSize: 12, color: AppColors.primary),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Text(
                "Current Status:",
                style: TextStyle(fontSize: 12, color: AppColors.primary),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: isWithinGeofence
                      ? AppColors.lightGreen200.withValues(alpha: 0.5)
                      : Colors.red.shade200.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  geofenceData.status,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 2),
                    child: Text(
                      "•",
                      style: TextStyle(fontSize: 18, color: AppColors.primary),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      isWithinGeofence
                          ? "Device is within approved school boundaries"
                          : "Device is outside approved boundaries",
                      style: TextStyle(fontSize: 12, color: AppColors.primary),
                      softWrap: true,
                    ),
                  ),
                ],
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 2),
                    child: Text(
                      "•",
                      style: TextStyle(fontSize: 18, color: AppColors.primary),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      "Location verified at ${geofenceData.lastVerified}",
                      style: TextStyle(fontSize: 12, color: AppColors.primary),
                      softWrap: true,
                    ),
                  ),
                ],
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 2),
                    child: Text(
                      "•",
                      style: TextStyle(fontSize: 18, color: AppColors.primary),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      isMonitoringActive
                          ? "Monitoring Active"
                          : "Monitoring Inactive",
                      style: TextStyle(fontSize: 12, color: AppColors.primary),
                      softWrap: true,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
