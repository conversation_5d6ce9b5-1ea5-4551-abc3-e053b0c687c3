import 'package:flutter/material.dart';
import '../../../shared/theme/app_colors.dart';
import 'section_card.dart';

class ScrollableSection extends StatelessWidget {
  final String title;
  final IconData titleIcon;
  final List<Widget> items;
  final double? maxHeight;
  final bool loading;
  final String emptyMessage;

  const ScrollableSection({
    super.key,
    required this.title,
    required this.titleIcon,
    required this.items,
    this.maxHeight,
    this.loading = false,
    this.emptyMessage = 'No items available',
  });

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    // Default responsive height
    final double responsiveHeight = (screenHeight * 0.35).clamp(250.0, 450.0);

    // Decide whether items fit without scroll
    final bool needsScroll = items.length > 4;

    Widget child;

    if (loading) {
      child = const Center(
        child: SizedBox(
          height: 20,
          width: 20,
          child: CircularProgressIndicator(
            strokeWidth: 3,
            color: AppColors.primary,
          ),
        ),
      );
    } else if (items.isEmpty) {
      child = Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            emptyMessage,
            style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
          ),
        ),
      );
    } else if (needsScroll) {
      child = SizedBox(
        height: maxHeight ?? responsiveHeight,
        child: ListView.builder(
          padding: EdgeInsets.zero,
          physics: const AlwaysScrollableScrollPhysics(),
          itemCount: items.length,
          itemBuilder: (context, index) => items[index],
        ),
      );
    } else {
      child = Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: items,
      );
    }

    return SectionCard(
      title: title,
      titleIcon: titleIcon,
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (widget, animation) =>
            FadeTransition(opacity: animation, child: widget),
        child: child,
      ),
    );
  }
}
