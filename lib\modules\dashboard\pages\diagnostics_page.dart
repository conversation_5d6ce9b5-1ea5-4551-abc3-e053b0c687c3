import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../shared/theme/app_colors.dart';
import '../../../shared/widgets/app_bar_widget.dart';
import '../../../shared/widgets/progress_bar.dart';
import '../../../shared/widgets/watermark.dart';
import '../controllers/diagnostics_controller.dart';
import '../widgets/diagnostics/diagnostic_run.dart';
import '../widgets/diagnostics/geofence_status.dart';
import '../widgets/diagnostics/system_health.dart';
import '../widgets/section_card.dart';
import '../widgets/status_progress.dart';

class DiagnosticsPage extends StatelessWidget {
  DiagnosticsPage({super.key});

  final DiagnosticsController controller = Get.find<DiagnosticsController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBarWidget(
        title: "System Diagnostics",
        showBackButton: true,
        onBack: () => Get.back(),
      ),
      body: Watermark(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Align(
            alignment: Alignment.topCenter,
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 1200),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Text(
                        "System Diagnostics",
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                      const Spacer(),
                      Obx(
                        () => LayoutBuilder(
                          builder: (context, constraints) {
                            final isSmall =
                                MediaQuery.of(context).size.width < 600;

                            return isSmall
                                ? IconButton(
                                    onPressed:
                                        controller.isRunningDiagnostics.value
                                        ? null
                                        : () => controller.runDiagnostics(),
                                    icon: controller.isRunningDiagnostics.value
                                        ? const SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              color: AppColors.primary,
                                            ),
                                          )
                                        : const Icon(
                                            Icons.refresh,
                                            color: AppColors.primary,
                                          ),
                                  )
                                : ElevatedButton.icon(
                                    onPressed:
                                        controller.isRunningDiagnostics.value
                                        ? null
                                        : () => controller.runDiagnostics(),
                                    style: ElevatedButton.styleFrom(
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      padding: const EdgeInsets.all(14),
                                    ),
                                    icon: controller.isRunningDiagnostics.value
                                        ? const SizedBox(
                                            width: 16,
                                            height: 16,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              color: Colors.white,
                                            ),
                                          )
                                        : const Icon(
                                            Icons.refresh,
                                            color: Colors.white,
                                          ),
                                    label: Text(
                                      controller.isRunningDiagnostics.value
                                          ? "Running..."
                                          : "Run Diagnostics",
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  );
                          },
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.2),
                          blurRadius: 10,
                          spreadRadius: 0,
                          offset: const Offset(0, 6),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(
                              Icons.trending_up_rounded,
                              color: AppColors.primary,
                              size: 22,
                            ),
                            const SizedBox(width: 8),
                            const Text(
                              "Performance Overview",
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: AppColors.primary,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 12),

                        Obx(
                          () => LayoutBuilder(
                            builder: (context, constraints) {
                              final isSmall = constraints.maxWidth < 600;

                              if (controller.isLoading.value) {
                                return const Center(
                                  child: CircularProgressIndicator(),
                                );
                              }

                              if (isSmall) {
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    StatusProgress(
                                      title: "Network Strength",
                                      subtitle:
                                          controller
                                              .networkMetrics
                                              ?.signalStatus ??
                                          "Unknown",
                                      percentage:
                                          controller.networkStrengthPercentage,
                                    ),
                                    const SizedBox(height: 16),
                                    StatusProgress(
                                      title: "RAM Usage",
                                      subtitle:
                                          controller.systemMetrics?.ramStatus ??
                                          "Unknown",
                                      percentage: controller.ramUsagePercentage,
                                    ),
                                  ],
                                );
                              }

                              return Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: StatusProgress(
                                      title: "Network Strength",
                                      subtitle:
                                          controller
                                              .networkMetrics
                                              ?.signalStatus ??
                                          "Unknown",
                                      percentage:
                                          controller.networkStrengthPercentage,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: StatusProgress(
                                      title: "RAM Usage",
                                      subtitle:
                                          controller.systemMetrics?.ramStatus ??
                                          "Unknown",
                                      percentage: controller.ramUsagePercentage,
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  LayoutBuilder(
                    builder: (context, constraints) {
                      final isSmall = constraints.maxWidth < 600;

                      if (isSmall) {
                        return Obx(
                          () => Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SectionCard(
                                title: "Network",
                                child: controller.isLoading.value
                                    ? const Center(
                                        child: Padding(
                                          padding: EdgeInsets.all(20),
                                          child: CircularProgressIndicator(),
                                        ),
                                      )
                                    : Column(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(10),
                                            decoration: BoxDecoration(
                                              color: Colors.grey.shade100
                                                  .withValues(alpha: 0.5),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              children: [
                                                Container(
                                                  padding: const EdgeInsets.all(
                                                    6,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: AppColors.primary
                                                        .withValues(
                                                          alpha: 0.08,
                                                        ),
                                                  ),
                                                  child: const Icon(
                                                    Icons.wifi,
                                                    color: AppColors.primary,
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                const Text("Connection"),
                                                const Spacer(),
                                                Text(
                                                  controller.networkStatus,
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(height: 16),
                                          Container(
                                            padding: const EdgeInsets.all(10),
                                            decoration: BoxDecoration(
                                              color: Colors.grey.shade100
                                                  .withValues(alpha: 0.5),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              children: [
                                                Container(
                                                  padding: const EdgeInsets.all(
                                                    6,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: AppColors.primary
                                                        .withValues(
                                                          alpha: 0.08,
                                                        ),
                                                  ),
                                                  child: const Icon(
                                                    Icons
                                                        .file_download_outlined,
                                                    color: AppColors.primary,
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                const Text("Download Speed"),
                                                const Spacer(),
                                                Text(
                                                  "${controller.networkMetrics?.downloadSpeed.toStringAsFixed(0) ?? '0'} Mbps",
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(height: 16),
                                          Container(
                                            padding: const EdgeInsets.all(10),
                                            decoration: BoxDecoration(
                                              color: Colors.grey.shade100
                                                  .withValues(alpha: 0.5),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              children: [
                                                Container(
                                                  padding: const EdgeInsets.all(
                                                    6,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: AppColors.primary
                                                        .withValues(
                                                          alpha: 0.08,
                                                        ),
                                                  ),
                                                  child: const Icon(
                                                    Icons.file_upload_outlined,
                                                    color: AppColors.primary,
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                const Text("Upload Speed"),
                                                const Spacer(),
                                                Text(
                                                  "${controller.networkMetrics?.uploadSpeed.toStringAsFixed(0) ?? '0'} Mbps",
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                              ),
                              const SizedBox(height: 16),
                              SectionCard(
                                title: "System",
                                child: controller.isLoading.value
                                    ? const Center(
                                        child: Padding(
                                          padding: EdgeInsets.all(20),
                                          child: CircularProgressIndicator(),
                                        ),
                                      )
                                    : Column(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(10),
                                            decoration: BoxDecoration(
                                              color: Colors.grey.shade100
                                                  .withValues(alpha: 0.5),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              children: [
                                                Container(
                                                  padding: const EdgeInsets.all(
                                                    6,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: AppColors.primary
                                                        .withValues(
                                                          alpha: 0.08,
                                                        ),
                                                  ),
                                                  child: const Icon(
                                                    Icons
                                                        .desktop_windows_outlined,
                                                    color: AppColors.primary,
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                const Text("Model ID"),
                                                const Spacer(),
                                                Text(
                                                  controller
                                                          .systemMetrics
                                                          ?.modelId ??
                                                      "Unknown",
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(height: 16),
                                          Container(
                                            padding: const EdgeInsets.all(10),
                                            decoration: BoxDecoration(
                                              color: Colors.grey.shade100
                                                  .withValues(alpha: 0.5),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              children: [
                                                Container(
                                                  padding: const EdgeInsets.all(
                                                    6,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: AppColors.primary
                                                        .withValues(
                                                          alpha: 0.08,
                                                        ),
                                                  ),
                                                  child: const Icon(
                                                    Icons.sd_storage_outlined,
                                                    color: AppColors.primary,
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                const Text("Storage"),
                                                const Spacer(),
                                                Text(
                                                  "${controller.systemMetrics?.storageUsedPercentage.toStringAsFixed(0) ?? '0'}% Used",
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(height: 16),
                                          Container(
                                            padding: const EdgeInsets.all(10),
                                            decoration: BoxDecoration(
                                              color: Colors.grey.shade100
                                                  .withValues(alpha: 0.5),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              children: [
                                                Container(
                                                  padding: const EdgeInsets.all(
                                                    6,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: AppColors.primary
                                                        .withValues(
                                                          alpha: 0.08,
                                                        ),
                                                  ),
                                                  child: const Icon(
                                                    Icons.timer_outlined,
                                                    color: AppColors.primary,
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                const Text("Uptime"),
                                                const Spacer(),
                                                Text(
                                                  controller
                                                          .systemMetrics
                                                          ?.uptime ??
                                                      "Unknown",
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                              ),
                            ],
                          ),
                        );
                      }

                      return Obx(
                        () => Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: SectionCard(
                                title: "Network",
                                child: controller.isLoading.value
                                    ? const Center(
                                        child: Padding(
                                          padding: EdgeInsets.all(20),
                                          child: CircularProgressIndicator(),
                                        ),
                                      )
                                    : Column(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(10),
                                            decoration: BoxDecoration(
                                              color: Colors.grey.shade100
                                                  .withValues(alpha: 0.5),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              children: [
                                                Container(
                                                  padding: const EdgeInsets.all(
                                                    6,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: AppColors.primary
                                                        .withValues(
                                                          alpha: 0.08,
                                                        ),
                                                  ),
                                                  child: const Icon(
                                                    Icons.wifi,
                                                    color: AppColors.primary,
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                const Text("Connection"),
                                                const Spacer(),
                                                Text(
                                                  controller.networkStatus,
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(height: 16),
                                          Container(
                                            padding: const EdgeInsets.all(10),
                                            decoration: BoxDecoration(
                                              color: Colors.grey.shade100
                                                  .withValues(alpha: 0.5),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              children: [
                                                Container(
                                                  padding: const EdgeInsets.all(
                                                    6,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: AppColors.primary
                                                        .withValues(
                                                          alpha: 0.08,
                                                        ),
                                                  ),
                                                  child: const Icon(
                                                    Icons
                                                        .file_download_outlined,
                                                    color: AppColors.primary,
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                const Text("Download Speed"),
                                                const Spacer(),
                                                Text(
                                                  "${controller.networkMetrics?.downloadSpeed.toStringAsFixed(0) ?? '0'} Mbps",
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(height: 16),
                                          Container(
                                            padding: const EdgeInsets.all(10),
                                            decoration: BoxDecoration(
                                              color: Colors.grey.shade100
                                                  .withValues(alpha: 0.5),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              children: [
                                                Container(
                                                  padding: const EdgeInsets.all(
                                                    6,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: AppColors.primary
                                                        .withValues(
                                                          alpha: 0.08,
                                                        ),
                                                  ),
                                                  child: const Icon(
                                                    Icons.file_upload_outlined,
                                                    color: AppColors.primary,
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                const Text("Upload Speed"),
                                                const Spacer(),
                                                Text(
                                                  "${controller.networkMetrics?.uploadSpeed.toStringAsFixed(0) ?? '0'} Mbps",
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: SectionCard(
                                title: "System",
                                child: controller.isLoading.value
                                    ? const Center(
                                        child: Padding(
                                          padding: EdgeInsets.all(20),
                                          child: CircularProgressIndicator(),
                                        ),
                                      )
                                    : Column(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(10),
                                            decoration: BoxDecoration(
                                              color: Colors.grey.shade100
                                                  .withValues(alpha: 0.5),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              children: [
                                                Container(
                                                  padding: const EdgeInsets.all(
                                                    6,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: AppColors.primary
                                                        .withValues(
                                                          alpha: 0.08,
                                                        ),
                                                  ),
                                                  child: const Icon(
                                                    Icons
                                                        .desktop_windows_outlined,
                                                    color: AppColors.primary,
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                const Text("Model ID"),
                                                const Spacer(),
                                                Text(
                                                  controller
                                                          .systemMetrics
                                                          ?.modelId ??
                                                      "Unknown",
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(height: 16),
                                          Container(
                                            padding: const EdgeInsets.all(10),
                                            decoration: BoxDecoration(
                                              color: Colors.grey.shade100
                                                  .withValues(alpha: 0.5),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              children: [
                                                Container(
                                                  padding: const EdgeInsets.all(
                                                    6,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: AppColors.primary
                                                        .withValues(
                                                          alpha: 0.08,
                                                        ),
                                                  ),
                                                  child: const Icon(
                                                    Icons.sd_storage_outlined,
                                                    color: AppColors.primary,
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                const Text("Storage"),
                                                const Spacer(),
                                                Text(
                                                  "${controller.systemMetrics?.storageUsedPercentage.toStringAsFixed(0) ?? '0'}% Used",
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(height: 16),
                                          Container(
                                            padding: const EdgeInsets.all(10),
                                            decoration: BoxDecoration(
                                              color: Colors.grey.shade100
                                                  .withValues(alpha: 0.5),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              children: [
                                                Container(
                                                  padding: const EdgeInsets.all(
                                                    6,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: AppColors.primary
                                                        .withValues(
                                                          alpha: 0.08,
                                                        ),
                                                  ),
                                                  child: const Icon(
                                                    Icons.timer_outlined,
                                                    color: AppColors.primary,
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                const Text("Uptime"),
                                                const Spacer(),
                                                Text(
                                                  controller
                                                          .systemMetrics
                                                          ?.uptime ??
                                                      "Unknown",
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 16),

                  SectionCard(
                    title: "Diagnostic Results",
                    titleIcon: Icons.monitor_heart_outlined,
                    child: Column(
                      children: [
                        Obx(
                          () => LayoutBuilder(
                            builder: (context, constraints) {
                              final isSmall = constraints.maxWidth < 600;

                              if (controller.isLoading.value) {
                                return const Center(
                                  child: Padding(
                                    padding: EdgeInsets.all(20),
                                    child: CircularProgressIndicator(),
                                  ),
                                );
                              }

                              if (isSmall) {
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SystemHealth(data: controller.systemHealth),
                                    const SizedBox(height: 16),
                                    GeofenceStatus(
                                      data: controller.geofenceData,
                                    ),
                                  ],
                                );
                              }

                              return Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: SystemHealth(
                                      fixedHeight: true,
                                      data: controller.systemHealth,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: GeofenceStatus(
                                      fixedHeight: true,
                                      data: controller.geofenceData,
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ),

                        const SizedBox(height: 16),

                        Obx(
                          () => DiagnosticRun(data: controller.diagnosticRun),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
