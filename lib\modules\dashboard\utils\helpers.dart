import 'package:flutter/material.dart';
import '../../../shared/models/activity.dart';
import '../widgets/activity_item.dart';

IconData getActivityIcon(String icon) {
  switch (icon.toLowerCase()) {
    case 'check':
      return Icons.check_circle_outline_rounded;
    case 'system':
      return Icons.computer;
    case 'user':
      return Icons.person;
    case 'alert':
      return Icons.warning;
    case 'sync':
      return Icons.sync;
    default:
      return Icons.info_outline;
  }
}

Color getActivityColor(String type) {
  switch (type.toLowerCase()) {
    case 'info':
      return Colors.blue.shade700;
    case 'alert':
      return Colors.orange.shade400;
    case 'success':
      return Colors.green;
    default:
      return Colors.grey;
  }
}

String formatTimeAgo(DateTime createdAt) {
  final now = DateTime.now();
  final difference = now.difference(createdAt);

  if (difference.inMinutes < 1) return 'Just now';
  if (difference.inHours < 1) return '${difference.inMinutes} minutes ago';
  if (difference.inDays < 1) return '${difference.inHours} hours ago';
  if (difference.inDays < 7) return '${difference.inDays} days ago';

  return '${(difference.inDays / 7).floor()} weeks ago';
}

ActivityItem mapActivityToItem(Activity activity) {
  return ActivityItem(
    icon: getActivityIcon(activity.icon!),
    text: activity.subject,
    time: formatTimeAgo(activity.createdAt),
    color: getActivityColor(activity.type),
  );
}
