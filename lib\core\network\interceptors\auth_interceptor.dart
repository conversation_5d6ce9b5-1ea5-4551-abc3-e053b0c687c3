import 'package:dio/dio.dart';
import 'package:get/get.dart';

import '../../../modules/onboarding/controllers/binding_code_controller.dart';
import '../../storage/secure_storage_service.dart';

class AuthInterceptor extends Interceptor {
  final SecureStorageService storage;

  AuthInterceptor(this.storage);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final token = await storage.readToken();
    if (token != null && token.isNotEmpty) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    return handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // If server responds 401, clear token and redirect to login
    if (err.response?.statusCode == 401) {
      try {
        final device = Get.find<BindingCodeController>();
        device.handleUnauthorized(); 
      } catch (_) {
        // ignore if BindingCodeController is not registered
      }
    }
    return handler.next(err);
  }
}