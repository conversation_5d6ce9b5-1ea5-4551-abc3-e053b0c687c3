class NetworkMetrics {
  final String connectionStatus;
  final double downloadSpeed; // in Mbps
  final double uploadSpeed; // in Mbps
  final int signalStrength; // percentage
  final String signalStatus;

  NetworkMetrics({
    required this.connectionStatus,
    required this.downloadSpeed,
    required this.uploadSpeed,
    required this.signalStrength,
    required this.signalStatus,
  });

  factory NetworkMetrics.mock() {
    return NetworkMetrics(
      connectionStatus: "Connected",
      downloadSpeed: 95.0,
      uploadSpeed: 45.0,
      signalStrength: 85,
      signalStatus: "Normal",
    );
  }
}

class SystemMetrics {
  final String modelId;
  final double storageUsedPercentage;
  final String uptime;
  final double ramUsagePercentage;
  final String ramStatus;

  SystemMetrics({
    required this.modelId,
    required this.storageUsedPercentage,
    required this.uptime,
    required this.ramUsagePercentage,
    required this.ramStatus,
  });

  factory SystemMetrics.mock() {
    return SystemMetrics(
      modelId: "EDU-TAB-2025",
      storageUsedPercentage: 45.0,
      uptime: "2d 14h",
      ramUsagePercentage: 64.0,
      ramStatus: "High",
    );
  }
}

class SystemHealthData {
  final String status;
  final String description;
  final int overallScore;
  final bool isHealthy;

  SystemHealthData({
    required this.status,
    required this.description,
    required this.overallScore,
    required this.isHealthy,
  });

  factory SystemHealthData.mock() {
    return SystemHealthData(
      status: "All systems operational",
      description: "System Health",
      overallScore: 98,
      isHealthy: true,
    );
  }
}

class GeofenceData {
  final String status;
  final String description;
  final bool isWithinGeofence;
  final String lastVerified;
  final bool isMonitoringActive;

  GeofenceData({
    required this.status,
    required this.description,
    required this.isWithinGeofence,
    required this.lastVerified,
    required this.isMonitoringActive,
  });

  factory GeofenceData.mock() {
    return GeofenceData(
      status: "Within Geofence",
      description: "Location monitoring",
      isWithinGeofence: true,
      lastVerified: "14:32",
      isMonitoringActive: true,
    );
  }
}

class DiagnosticRunData {
  final DateTime date;
  final Duration duration;
  final int testsRun;
  final String status;
  final bool allPassed;

  DiagnosticRunData({
    required this.date,
    required this.duration,
    required this.testsRun,
    required this.status,
    required this.allPassed,
  });

  factory DiagnosticRunData.mock() {
    return DiagnosticRunData(
      date: DateTime.now(),
      duration: const Duration(seconds: 23),
      testsRun: 12,
      status: "All Passed",
      allPassed: true,
    );
  }

  String get formattedDate {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dateOnly = DateTime(date.year, date.month, date.day);
    
    if (dateOnly == today) {
      return "Today, ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}";
    } else {
      return "${date.day}/${date.month}/${date.year}, ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}";
    }
  }

  String get formattedDuration {
    if (duration.inMinutes > 0) {
      return "${duration.inMinutes}m ${duration.inSeconds % 60}s";
    } else {
      return "${duration.inSeconds} seconds";
    }
  }
}

class DiagnosticData {
  final NetworkMetrics networkMetrics;
  final SystemMetrics systemMetrics;
  final SystemHealthData systemHealth;
  final GeofenceData geofenceData;
  final DiagnosticRunData diagnosticRun;

  DiagnosticData({
    required this.networkMetrics,
    required this.systemMetrics,
    required this.systemHealth,
    required this.geofenceData,
    required this.diagnosticRun,
  });

  factory DiagnosticData.mock() {
    return DiagnosticData(
      networkMetrics: NetworkMetrics.mock(),
      systemMetrics: SystemMetrics.mock(),
      systemHealth: SystemHealthData.mock(),
      geofenceData: GeofenceData.mock(),
      diagnosticRun: DiagnosticRunData.mock(),
    );
  }
}
