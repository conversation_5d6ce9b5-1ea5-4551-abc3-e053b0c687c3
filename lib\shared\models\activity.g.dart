// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Activity _$ActivityFromJson(Map<String, dynamic> json) => Activity(
  id: (json['id'] as num).toInt(),
  userId: (json['userId'] as num?)?.toInt(),
  deviceId: (json['deviceId'] as num?)?.toInt(),
  subject: json['subject'] as String,
  description: json['description'] as String?,
  type: json['type'] as String,
  status: json['status'] as String?,
  icon: json['icon'] as String?,
  user: json['user'],
  device: json['device'] == null
      ? null
      : Device.fromJson(json['device'] as Map<String, dynamic>),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$ActivityToJson(Activity instance) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'deviceId': instance.deviceId,
  'subject': instance.subject,
  'description': instance.description,
  'type': instance.type,
  'status': instance.status,
  'icon': instance.icon,
  'user': ?instance.user,
  'device': ?instance.device,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};
