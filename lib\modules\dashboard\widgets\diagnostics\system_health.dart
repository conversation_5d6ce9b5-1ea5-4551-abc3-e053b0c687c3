import 'package:flutter/material.dart';

import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/widgets/progress_bar.dart';
import '../../data/models/diagnostic_data.dart';

class SystemHealth extends StatelessWidget {
  final bool fixedHeight;
  final SystemHealthData? data;

  const SystemHealth({super.key, this.fixedHeight = false, this.data});

  @override
  Widget build(BuildContext context) {
    final healthData = data ?? SystemHealthData.mock();
    final isHealthy = healthData.isHealthy;
    final score = healthData.overallScore;

    return Container(
      height: fixedHeight ? 200 : null,
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: isHealthy ? AppColors.lightGreen50 : Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isHealthy ? AppColors.lightGreen200 : Colors.red.shade200,
        ),
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                isHealthy
                    ? Icons.check_circle_outline_rounded
                    : Icons.warning_amber_rounded,
                color: isHealthy
                    ? const Color(0xFF38a169)
                    : Colors.red.shade600,
                size: 30,
              ),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    healthData.description,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    healthData.status,
                    style: TextStyle(fontSize: 12, color: AppColors.primary),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Text(
                "Overall Score:",
                style: TextStyle(fontSize: 12, color: AppColors.primary),
              ),
              const Spacer(),
              Text(
                "$score/100",
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ProgressBar(
            value: score / 100,
            color: isHealthy ? AppColors.primary : Colors.red.shade600,
          ),
        ],
      ),
    );
  }
}
