import 'package:dio/dio.dart';

import '../../../../core/errors/app_exception.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../shared/models/activity.dart';

class DashboardRepository {
  final DioClient dioClient;

  DashboardRepository(this.dioClient);

  Future<List<Activity>> getRecentActivities() async {
    try {
      final response = await dioClient.dio.get(
        "/app/dashboard/recent-activities",
      );

      final data = response.data;
      if (data is! Map<String, dynamic> || data["DATA"] == null) {
        throw ServerException("Invalid response format");
      }

      final rawData = data["DATA"];
      if (rawData is! List) throw ServerException("Expected list of activities");

      return rawData
          .whereType<Map<String, dynamic>>()
          .map(Activity.fromJson)
          .toList();
    } on DioException catch (e) {
      throw e.error as AppException;
    }
  }
}
