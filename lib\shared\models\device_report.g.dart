// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_report.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeviceReport _$DeviceReportFromJson(Map<String, dynamic> json) => DeviceReport(
  id: (json['id'] as num).toInt(),
  deviceId: (json['deviceId'] as num).toInt(),
  signalStrength: (json['signalStrength'] as num).toInt(),
  batteryLevel: (json['batteryLevel'] as num).toInt(),
  temperature: (json['temperature'] as num).toInt(),
  signalStatus: json['signalStatus'] as String,
  diskSize: (json['diskSize'] as num).toInt(),
  diskFree: (json['diskFree'] as num).toInt(),
  ramSize: (json['ramSize'] as num).toInt(),
  ramFree: (json['ramFree'] as num).toInt(),
  device: json['device'] == null
      ? null
      : Device.fromJson(json['device'] as Map<String, dynamic>),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$DeviceReportToJson(DeviceReport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'deviceId': instance.deviceId,
      'signalStrength': instance.signalStrength,
      'batteryLevel': instance.batteryLevel,
      'temperature': instance.temperature,
      'signalStatus': instance.signalStatus,
      'diskSize': instance.diskSize,
      'diskFree': instance.diskFree,
      'ramSize': instance.ramSize,
      'ramFree': instance.ramFree,
      'device': instance.device,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
