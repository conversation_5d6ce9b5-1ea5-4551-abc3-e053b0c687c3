import 'package:get/get.dart';
import '../../../core/network/dio_client.dart';
import '../controllers/dashboard_controller.dart';
import '../data/repositories/dashboard_repository.dart';

class DashboardBinding extends Bindings {
  @override
  void dependencies() {
    Get.put<DashboardRepository>(DashboardRepository(Get.find<DioClient>()));
    Get.put(DashboardController(Get.find<DashboardRepository>()));
  }
}