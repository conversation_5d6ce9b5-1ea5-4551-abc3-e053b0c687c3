import 'package:get/get.dart';
import '../../../core/network/dio_client.dart';
import '../controllers/dashboard_controller.dart';
import '../controllers/diagnostics_controller.dart';
import '../data/repositories/dashboard_repository.dart';
import '../data/repositories/diagnostics_repository.dart';

class DashboardBinding extends Bindings {
  @override
  void dependencies() {
    // Dashboard dependencies
    Get.put<DashboardRepository>(DashboardRepository(Get.find<DioClient>()));
    Get.put(DashboardController(Get.find<DashboardRepository>()));

    // Diagnostics dependencies
    Get.put<DiagnosticsRepository>(
      DiagnosticsRepository(Get.find<DioClient>()),
    );
    Get.put(DiagnosticsController(Get.find<DiagnosticsRepository>()));
  }
}
