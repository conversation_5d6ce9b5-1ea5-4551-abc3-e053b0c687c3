import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart';

import '../../../../core/errors/app_exception.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../shared/models/binding_code.dart';
import '../../../../shared/models/device.dart';

class BindingRepository {
  final DioClient dioClient;

  BindingRepository(this.dioClient);

  Future<Device> getDevice() async {
    final response = await dioClient.dio.get("/app/me");
    
    if (response.statusCode == 200 && response.data["DATA"] != null) {
      return Device.fromJson(response.data["DATA"]);
    } else {
      throw Exception("Invalid response from server");
    }
  }

  Future<List<Map<String, dynamic>>> getDeviceTypes() async {
    final response = await dioClient.dio.get("/core/device-types");
    
    if (response.statusCode == 200 && response.data["DATA"] != null) {
      // Ensure list of maps
      final List data = response.data["DATA"];
      return data.map((e) => e as Map<String, dynamic>).toList();
    } else {
      throw Exception("Invalid response from server");
    }
  }

  Future<BindingCode> validateBindingCode({
    required String bindingCode,
    required String deviceType,
  }) async {
    try {
      final response = await dioClient.dio.post(
        "/app/binding/validate",
        data: {
          "binding_code": bindingCode,
          "device_type": deviceType,
        },
      );

      if (response.data == null || response.data["DATA"] == null) {
        throw ServerException("Invalid response from server");
      }

      return BindingCode.fromJson(response.data["DATA"]);
    } on DioException catch (e) {
      throw e.error as AppException;
    }
  }

  Future<Map<String, dynamic>> bindDevice({
    required String bindingCode,
    required String deviceType,
  }) async {
    final deviceIdentifier = await getDeviceIdentifier();
    print(deviceIdentifier);
    if (deviceIdentifier == null) {
      throw ServerException("Device identifier not found");
    }
    
    try {
      final response = await dioClient.dio.post("/app/binding/bind", data: {
        "binding_code": bindingCode,
        "device_type": deviceType,
        "identifier": deviceIdentifier,
      });

      if (response.data == null || response.data["DATA"] == null) {
        throw ServerException("Invalid response from server");
      }

      return response.data["DATA"] as Map<String, dynamic>;
    } on DioException catch (e) {
      throw e.error as AppException;
    }
  }

  Future<void> logout() async {
    try {
      await dioClient.dio.post('/app/logout');
    } catch (e) {
      // ignore errors
    }
  }

  Future<String?> getDeviceIdentifier() async {
    final deviceInfo = DeviceInfoPlugin();

    if (GetPlatform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      return androidInfo.id;
    } else if (GetPlatform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      return iosInfo.identifierForVendor;
    } else if (GetPlatform.isWeb) {
      final webInfo = await deviceInfo.webBrowserInfo;
      return webInfo.data['identifierForVendor'] ?? 'web';
    }
    return null;
  }
}
