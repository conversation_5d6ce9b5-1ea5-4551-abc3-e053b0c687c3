import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../shared/theme/app_colors.dart';
import '../../../shared/widgets/app_bar_widget.dart';
import '../../../shared/widgets/watermark.dart';
import '../controllers/dashboard_controller.dart';
import '../widgets/activity_item.dart';
import '../widgets/alert_item.dart';
import '../widgets/scrollable_section.dart';
import '../widgets/status_card.dart';

class DashboardPage extends StatelessWidget {
  DashboardPage({super.key});

  final DashboardController controller = Get.find<DashboardController>();

  final List<StatusCardData> statusCards = [
    StatusCardData(
      icon: Icons.wifi,
      title: "Network Status",
      value: "Connected",
      subtitle: "Signal Strength: 98%",
      status: "Pending",
      iconColor: Colors.green,
    ),
    StatusCardData(
      icon: Icons.desktop_windows_outlined,
      title: "System Health",
      value: "Optimal",
      subtitle: "All systems operational",
      iconColor: Colors.green,
    ),
    StatusCardData(
      icon: Icons.timer_outlined,
      title: "Monitoring Uptime",
      value: "99.8%",
      subtitle: "Device monitoring availability",
      iconColor: Colors.green,
    ),
    StatusCardData(
      icon: Icons.trending_up,
      title: "Performance",
      value: "95%",
      subtitle: "System performance score",
      iconColor: Colors.green,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final isWide = MediaQuery.of(context).size.width > 800;
    final isSmall = MediaQuery.of(context).size.width < 500;

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBarWidget(title: "System Dashboard"),
      body: Watermark(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Align(
            alignment: Alignment.topCenter,
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 1200),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Text(
                        "System Status Overview",
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                      const Spacer(),
                      LayoutBuilder(
                        builder: (context, constraints) {
                          final isSmall =
                              MediaQuery.of(context).size.width < 600;

                          return Row(
                            children: [
                              isSmall
                                  ? IconButton(
                                      onPressed: () {},
                                      icon: const Icon(
                                        Icons.refresh,
                                        color: AppColors.primary,
                                      ),
                                    )
                                  : OutlinedButton.icon(
                                      onPressed: () {},
                                      style: OutlinedButton.styleFrom(
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            6,
                                          ),
                                        ),
                                        padding: const EdgeInsets.all(14),
                                        side: const BorderSide(
                                          color: AppColors.primary,
                                        ),
                                      ),
                                      icon: const Icon(
                                        Icons.refresh,
                                        color: AppColors.primary,
                                      ),
                                      label: const Text(
                                        "Refresh",
                                        style: TextStyle(
                                          color: AppColors.primary,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ),
                              const SizedBox(width: 16),
                              isSmall
                                  ? IconButton(
                                      onPressed: () =>
                                          Get.toNamed('/dashboard/diagnostics'),
                                      icon: const Icon(
                                        Icons.insert_chart_outlined,
                                        color: AppColors.primary,
                                      ),
                                    )
                                  : ElevatedButton.icon(
                                      onPressed: () =>
                                          Get.toNamed('/dashboard/diagnostics'),
                                      style: ElevatedButton.styleFrom(
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            6,
                                          ),
                                        ),
                                        padding: const EdgeInsets.all(14),
                                      ),
                                      icon: const Icon(
                                        Icons.insert_chart_outlined,
                                        color: Colors.white,
                                      ),
                                      label: const Text(
                                        "Diagnostics",
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: isWide
                          ? 4
                          : isSmall
                          ? 1
                          : 2,
                      crossAxisSpacing: 10,
                      mainAxisSpacing: 8,
                      mainAxisExtent: 180,
                    ),
                    itemCount: statusCards.length,
                    itemBuilder: (context, index) {
                      final card = statusCards[index];
                      return StatusCard(
                        icon: card.icon,
                        title: card.title,
                        value: card.value,
                        subtitle: card.subtitle,
                        status: card.status,
                        iconColor: card.iconColor,
                      );
                    },
                  ),

                  const SizedBox(height: 24),

                  // Activity and Alerts Section
                  LayoutBuilder(
                    builder: (context, constraints) {
                      final isSmall = constraints.maxWidth < 600;

                      final alertItems = [
                        AlertItem(
                          icon: Icons.warning_amber_rounded,
                          title: "Software Update Available",
                          subtitle:
                              "New monitoring features are available for installation",
                          time: "2 hours ago",
                          color: Colors.orange.shade400,
                        ),
                        AlertItem(
                          icon: Icons.schedule,
                          title: "Scheduled Maintenance",
                          subtitle:
                              "System maintenance planned for this weekend",
                          time: "1 day ago",
                          color: Colors.blue.shade700,
                        ),
                      ];

                      if (isSmall) {
                        return Column(
                          children: [
                            Obx(
                              () => ScrollableSection(
                                title: "Recent Activity",
                                titleIcon: Icons.access_time,
                                emptyMessage: "No recent activities",
                                items: controller.activityItems,
                                loading: controller.isLoadingActivities.value,
                                maxHeight: 300,
                              ),
                            ),
                            const SizedBox(height: 16),
                            ScrollableSection(
                              title: "System Alerts",
                              titleIcon: Icons.warning_amber_rounded,
                              items: alertItems,
                              maxHeight: 300,
                            ),
                          ],
                        );
                      }

                      return Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Obx(
                              () => ScrollableSection(
                                title: "Recent Activity",
                                titleIcon: Icons.access_time,
                                emptyMessage: "No recent activities",
                                items: controller.activityItems,
                                loading: controller.isLoadingActivities.value,
                                maxHeight: 400,
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ScrollableSection(
                              title: "System Alerts",
                              titleIcon: Icons.warning_amber_rounded,
                              items: alertItems,
                              maxHeight: 400,
                            ),
                          ),
                        ],
                      );
                    },
                  ),

                  const SizedBox(height: 24),

                  // ✅ Footer
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.lightGreen50.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.lightGreen200),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.2),
                          blurRadius: 10,
                          offset: const Offset(0, 6),
                        ),
                      ],
                    ),
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        final isSmallScreen =
                            constraints.maxWidth < 400; // breakpoint
                        if (isSmallScreen) {
                          // 📱 Column layout (stacked)
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Smart School Monitor",
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primary,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                "Last updated: 9/6/2025, 11:16:51 AM",
                                style: const TextStyle(fontSize: 12),
                              ),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  _buildBadge("Version 2.1.0", outlined: true),
                                  const SizedBox(width: 8),
                                  _buildBadge("Connected", filled: true),
                                ],
                              ),
                            ],
                          );
                        } else {
                          // 🖥 Row layout (like your screenshot)
                          return Row(
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Smart School Monitor",
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.primary,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  const Text(
                                    "Last updated: 9/6/2025, 11:16:51 AM",
                                    style: TextStyle(fontSize: 12),
                                  ),
                                ],
                              ),
                              const Spacer(),
                              _buildBadge("Version 2.1.0", outlined: true),
                              const SizedBox(width: 8),
                              _buildBadge("Connected", filled: true),
                            ],
                          );
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Helper methods for activity display
IconData _getActivityIcon(String type) {
  switch (type.toLowerCase()) {
    case 'system':
      return Icons.computer;
    case 'user':
      return Icons.person;
    case 'alert':
      return Icons.warning;
    case 'sync':
      return Icons.sync;
    default:
      return Icons.info_outline;
  }
}

Color _getActivityColor(String type) {
  switch (type.toLowerCase()) {
    case 'system':
      return Colors.blue.shade700;
    case 'alert':
      return Colors.orange.shade400;
    case 'success':
      return Colors.green;
    default:
      return Colors.grey;
  }
}

String _formatTimeAgo(DateTime dateTime) {
  final now = DateTime.now();
  final difference = now.difference(dateTime);

  if (difference.inMinutes < 1) return 'Just now';
  if (difference.inHours < 1) return '${difference.inMinutes} minutes ago';
  if (difference.inDays < 1) return '${difference.inHours} hours ago';
  if (difference.inDays < 7) return '${difference.inDays} days ago';

  return '${(difference.inDays / 7).floor()} weeks ago';
}

class StatusCardData {
  final IconData icon;
  final String title;
  final String value;
  final String subtitle;
  final String status;
  final Color iconColor;

  StatusCardData({
    required this.icon,
    required this.title,
    required this.value,
    required this.subtitle,
    this.status = 'Active',
    this.iconColor = Colors.green,
  });
}

Widget _buildBadge(String text, {bool outlined = false, bool filled = false}) {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    decoration: BoxDecoration(
      color: filled ? AppColors.lightGreen200.withValues(alpha: 0.5) : null,
      border: outlined ? Border.all(color: AppColors.primary) : null,
      borderRadius: BorderRadius.circular(6),
    ),
    child: Text(
      text,
      style: TextStyle(
        fontSize: 10,
        color: AppColors.primary,
        fontWeight: FontWeight.w500,
      ),
    ),
  );
}
