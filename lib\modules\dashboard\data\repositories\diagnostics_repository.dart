import 'package:dio/dio.dart';

import '../../../../core/errors/app_exception.dart';
import '../../../../core/network/dio_client.dart';
import '../models/diagnostic_data.dart';

class DiagnosticsRepository {
  final DioClient dioClient;

  DiagnosticsRepository(this.dioClient);

  /// Get diagnostic data for the device
  /// Currently returns mock data, but can be extended to fetch from API
  Future<DiagnosticData> getDiagnosticData() async {
    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 800));
      
      // For now, return mock data
      // In the future, this would make an API call like:
      // final response = await dioClient.dio.get("/app/diagnostics");
      // return DiagnosticData.fromJson(response.data["DATA"]);
      
      return DiagnosticData.mock();
    } on DioException catch (e) {
      throw e.error as AppException;
    } catch (e) {
      throw ServerException("Failed to fetch diagnostic data");
    }
  }

  /// Run diagnostics and get updated data
  Future<DiagnosticData> runDiagnostics() async {
    try {
      // Simulate diagnostic run delay
      await Future.delayed(const Duration(seconds: 2));
      
      // For now, return mock data with updated timestamp
      // In the future, this would make an API call like:
      // final response = await dioClient.dio.post("/app/diagnostics/run");
      // return DiagnosticData.fromJson(response.data["DATA"]);
      
      return DiagnosticData.mock();
    } on DioException catch (e) {
      throw e.error as AppException;
    } catch (e) {
      throw ServerException("Failed to run diagnostics");
    }
  }

  /// Get network metrics specifically
  Future<NetworkMetrics> getNetworkMetrics() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      return NetworkMetrics.mock();
    } on DioException catch (e) {
      throw e.error as AppException;
    } catch (e) {
      throw ServerException("Failed to fetch network metrics");
    }
  }

  /// Get system metrics specifically
  Future<SystemMetrics> getSystemMetrics() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      return SystemMetrics.mock();
    } on DioException catch (e) {
      throw e.error as AppException;
    } catch (e) {
      throw ServerException("Failed to fetch system metrics");
    }
  }
}
