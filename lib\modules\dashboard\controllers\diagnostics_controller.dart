import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/errors/error_handler.dart';
import '../../../core/services/device_auth_service.dart';
import '../data/models/diagnostic_data.dart';
import '../data/repositories/diagnostics_repository.dart';

class DiagnosticsController extends GetxController {
  final DeviceAuthService authService = Get.find<DeviceAuthService>();
  final DiagnosticsRepository diagnosticsRepository;

  // Reactive state variables
  final Rxn<DiagnosticData> _diagnosticData = Rxn<DiagnosticData>();
  final RxBool isLoading = false.obs;
  final RxBool isRunningDiagnostics = false.obs;
  final RxnString errorMessage = RxnString();

  DiagnosticsController(this.diagnosticsRepository);

  // Getters for accessing reactive data
  DiagnosticData? get diagnosticData => _diagnosticData.value;
  NetworkMetrics? get networkMetrics => _diagnosticData.value?.networkMetrics;
  SystemMetrics? get systemMetrics => _diagnosticData.value?.systemMetrics;
  SystemHealthData? get systemHealth => _diagnosticData.value?.systemHealth;
  GeofenceData? get geofenceData => _diagnosticData.value?.geofenceData;
  DiagnosticRunData? get diagnosticRun => _diagnosticData.value?.diagnosticRun;

  @override
  void onInit() {
    super.onInit();
    _init();
  }

  Future<void> _init() async {
    await authService.getDevice();
    await loadDiagnosticData();
  }

  /// Load diagnostic data from repository
  Future<void> loadDiagnosticData() async {
    if (isLoading.value) return; // Prevent multiple simultaneous calls

    isLoading.value = true;
    errorMessage.value = null;

    try {
      final data = await diagnosticsRepository.getDiagnosticData();
      _diagnosticData.value = data;
    } catch (e) {
      errorMessage.value = e.toString();
      ErrorHandler.handle(e, retry: loadDiagnosticData);
    } finally {
      isLoading.value = false;
    }
  }

  /// Run diagnostics and refresh data
  Future<void> runDiagnostics() async {
    if (isRunningDiagnostics.value) {
      return; // Prevent multiple simultaneous runs
    }

    isRunningDiagnostics.value = true;
    errorMessage.value = null;

    try {
      final data = await diagnosticsRepository.runDiagnostics();
      _diagnosticData.value = data;

      // Show success message
      Get.snackbar(
        "Success",
        "Diagnostics completed successfully",
        backgroundColor: Get.theme.primaryColor,
        colorText: Get.theme.colorScheme.onPrimary,
        snackPosition: SnackPosition.BOTTOM,
        margin: const EdgeInsets.all(12),
        borderRadius: 12,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      errorMessage.value = e.toString();
      ErrorHandler.handle(e, retry: runDiagnostics);
    } finally {
      isRunningDiagnostics.value = false;
    }
  }

  /// Refresh diagnostic data (pull-to-refresh)
  Future<void> refreshData() async {
    await loadDiagnosticData();
  }

  /// Get network strength percentage for progress indicators
  double get networkStrengthPercentage {
    return networkMetrics?.signalStrength.toDouble() ?? 0.0;
  }

  /// Get RAM usage percentage for progress indicators
  double get ramUsagePercentage {
    return systemMetrics?.ramUsagePercentage ?? 0.0;
  }

  /// Get storage usage percentage
  double get storageUsagePercentage {
    return systemMetrics?.storageUsedPercentage ?? 0.0;
  }

  /// Get system health score percentage
  double get systemHealthPercentage {
    return systemHealth?.overallScore.toDouble() ?? 0.0;
  }

  /// Check if system is healthy
  bool get isSystemHealthy {
    return systemHealth?.isHealthy ?? false;
  }

  /// Check if device is within geofence
  bool get isWithinGeofence {
    return geofenceData?.isWithinGeofence ?? false;
  }

  /// Get formatted network status
  String get networkStatus {
    return networkMetrics?.connectionStatus ?? "Unknown";
  }

  /// Get formatted system status
  String get systemStatus {
    return systemHealth?.status ?? "Unknown";
  }

  /// Get formatted geofence status
  String get geofenceStatus {
    return geofenceData?.status ?? "Unknown";
  }

  @override
  void onClose() {
    // Clean up any resources if needed
    super.onClose();
  }
}
