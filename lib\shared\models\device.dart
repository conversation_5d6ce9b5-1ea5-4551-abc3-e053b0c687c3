import 'package:json_annotation/json_annotation.dart';
import 'school.dart';
import 'device_report.dart';

part 'device.g.dart';

@JsonSerializable()
class Device {
  final int id;
  final String code;
  final String location;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'lastReportedAt')
  final DateTime lastReportedAt;
  @J<PERSON><PERSON><PERSON>(name: 'deviceStatus')
  final String deviceStatus;
  final String status;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'deviceTypeId')
  final int deviceTypeId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'deviceType')
  final String deviceType;
  @<PERSON><PERSON><PERSON>ey(name: 'reports')
  final List<DeviceReport?> reports;
  @J<PERSON><PERSON><PERSON>(name: 'school')
  final School? school;
  @Json<PERSON>ey(name: 'createdAt')
  final DateTime createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updatedAt')
  final DateTime updatedAt;

  const Device({
    required this.id,
    required this.code,
    required this.location,
    required this.lastReportedAt,
    required this.deviceStatus,
    required this.status,
    required this.deviceTypeId,
    required this.deviceType,
    this.reports = const [],
    this.school,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Connect the generated [_$DeviceFromJson] function to the `fromJson`
  /// factory.
  factory Device.fromJson(Map<String, dynamic> json) => _$DeviceFromJson(json);

  /// Connect the generated [_$DeviceToJson] function to the `toJson` method.
  Map<String, dynamic> toJson() => _$DeviceToJson(this);

  @override
  String toString() {
    return 'Device{id: $id, code: $code, type: $deviceType, status: $status}';
  }

  /// Creates a copy of the Device with the given fields replaced by the non-null values.
  Device copyWith({
    int? id,
    String? code,
    String? location,
    DateTime? lastReportedAt,
    String? deviceStatus,
    String? status,
    int? deviceTypeId,
    String? deviceType,
    List<DeviceReport?>? reports,
    School? school,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Device(
      id: id ?? this.id,
      code: code ?? this.code,
      location: location ?? this.location,
      lastReportedAt: lastReportedAt ?? this.lastReportedAt,
      deviceStatus: deviceStatus ?? this.deviceStatus,
      status: status ?? this.status,
      deviceTypeId: deviceTypeId ?? this.deviceTypeId,
      deviceType: deviceType ?? this.deviceType,
      reports: reports ?? this.reports,
      school: school ?? this.school,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Returns the most recent report if available
  DeviceReport? get latestReport => reports.isNotEmpty ? reports.first : null;

  /// Returns true if the device is currently online
  bool get isOnline {
    if (lastReportedAt == null) return false;
    final difference = DateTime.now().difference(lastReportedAt);
    return difference.inMinutes < 5; // Considered online if reported in last 5 minutes
  }

  /// Returns the battery level from the latest report, or null if not available
  int? get batteryLevel => latestReport?.batteryLevel;

  /// Returns the temperature from the latest report, or null if not available
  int? get temperature => latestReport?.temperature;

  /// Returns the signal strength from the latest report, or null if not available
  int? get signalStrength => latestReport?.signalStrength;

  /// Returns the disk usage percentage from the latest report, or null if not available
  double? get diskUsagePercentage => latestReport?.diskUsagePercentage;

  /// Returns the RAM usage percentage from the latest report, or null if not available
  double? get ramUsagePercentage => latestReport?.ramUsagePercentage;
}